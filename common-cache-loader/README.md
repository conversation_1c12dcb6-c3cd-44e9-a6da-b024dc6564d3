# Common Cache Loader Library

## Overview
A thread-safe, production-ready caching library for Spring Boot applications that provides in-memory caching with configurable refresh strategies. The library follows the Strategy pattern for cache refresh mechanisms and provides REST endpoints for cache management.

## Features
- **Thread-safe operations** using ConcurrentHashMap and ReadWriteLock
- **Strategy pattern** for flexible cache refresh mechanisms
- **REST endpoints** for cache management and monitoring
- **Auto-configuration** for easy Spring Boot integration
- **PostConstruct initialization** for automatic cache population
- **Comprehensive logging** with configurable levels
- **Generic type support** for any data type
- **Defensive copying** to prevent external modifications

## Quick Start

### 1. Add Dependency
Add the following dependency to your `pom.xml`:

```xml
<dependency>
    <groupId>com.sfn</groupId>
    <artifactId>common-cache-loader</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. Basic Usage

```java
@Service
@RequiredArgsConstructor
public class MyService {
    
    private final CacheManager<MyDataBean> cacheManager;
    
    @PostConstruct
    public void initializeCache() {
        // Register refresh strategy
        cacheManager.registerRefreshStrategy("mydata", () -> {
            // Load data from database or external service
            return myRepository.findAll();
        });
        
        // Initial cache population
        cacheManager.refresh("mydata");
    }
    
    public List<MyDataBean> getMyData() {
        return cacheManager.get("mydata").orElse(List.of());
    }
}
```

### 3. Configuration
Configure the cache behavior in your `application.yml`:

```yaml
sfn:
  cache:
    refresh-endpoints-enabled: true  # Enable REST endpoints
    auto-initialize: true            # Auto-initialize on startup
    debug-logging: false            # Enable debug logging
    max-cache-size: 1000            # Maximum cache entries
    fail-fast-on-init-error: false  # Fail on initialization errors
```

## API Reference

### CacheManager Interface

#### Core Operations
- `put(String key, List<T> data)` - Store data in cache
- `get(String key)` - Retrieve data from cache
- `refresh(String key)` - Refresh specific cache entry
- `refreshAll()` - Refresh all cache entries
- `containsKey(String key)` - Check if key exists
- `remove(String key)` - Remove cache entry
- `clear()` - Clear all cache entries
- `size()` - Get cache size
- `getKeys()` - Get all cache keys

#### Strategy Management
- `registerRefreshStrategy(String key, RefreshStrategy<T> strategy)` - Register refresh strategy

### RefreshStrategy Interface

```java
@FunctionalInterface
public interface RefreshStrategy<T> {
    List<T> loadData() throws Exception;
}
```

## REST Endpoints

When `refresh-endpoints-enabled` is true, the following endpoints are available:

- `POST /api/cache/refresh/{key}` - Refresh specific cache
- `POST /api/cache/refresh-all` - Refresh all caches
- `GET /api/cache/status` - Get cache status and statistics
- `DELETE /api/cache/clear/{key}` - Clear specific cache
- `DELETE /api/cache/clear-all` - Clear all caches

## Advanced Usage

### Custom Refresh Strategy

```java
@Component
public class DatabaseRefreshStrategy implements RefreshStrategy<MyEntity> {
    
    @Autowired
    private MyRepository repository;
    
    @Override
    public List<MyEntity> loadData() throws Exception {
        try {
            return repository.findAllActive();
        } catch (Exception e) {
            log.error("Failed to load data from database", e);
            throw new Exception("Database refresh failed", e);
        }
    }
}
```

### Service with Multiple Caches

```java
@Service
@RequiredArgsConstructor
public class MultiCacheService {
    
    private final CacheManager<Object> cacheManager;
    private final UserRefreshStrategy userStrategy;
    private final ProductRefreshStrategy productStrategy;
    
    @PostConstruct
    public void initializeCaches() {
        // Register multiple strategies
        cacheManager.registerRefreshStrategy("users", userStrategy);
        cacheManager.registerRefreshStrategy("products", productStrategy);
        
        // Initialize all caches
        cacheManager.refreshAll();
    }
}
```

## Thread Safety
The library is fully thread-safe:
- Uses `ConcurrentHashMap` for cache storage
- Uses `ReadWriteLock` for refresh operations
- Provides defensive copying to prevent external modifications
- Safe for concurrent read/write operations

## Error Handling
- Comprehensive exception handling in refresh operations
- Configurable fail-fast behavior
- Detailed logging for troubleshooting
- Graceful degradation on refresh failures

## Best Practices

1. **Initialize in @PostConstruct**: Always register strategies and perform initial refresh in `@PostConstruct` methods
2. **Handle Empty Results**: Always handle `Optional.empty()` results from cache operations
3. **Use Defensive Copying**: The library provides defensive copying, but be aware of object mutability
4. **Monitor Cache Health**: Use the status endpoint to monitor cache health
5. **Implement Proper Error Handling**: Always handle exceptions in refresh strategies
6. **Use Appropriate Keys**: Use descriptive, unique keys for different cache types

## Example Implementation
See the `com.sfn.cache.example` package for a complete example implementation with:
- `AcquirerBean` - Example domain object
- `AcquirerRefreshStrategy` - Example refresh strategy
- `ExampleCacheService` - Example service usage

## Logging
The library uses Log4j2 for logging. Configure the following loggers:
- `com.sfn.cache` - General cache operations
- `com.sfn.cache.impl.InMemoryCacheManager` - Cache manager operations
- `com.sfn.cache.controller.CacheRefreshController` - REST endpoint operations

## Dependencies
- Spring Boot Starter
- Spring Boot Starter Web
- Lombok
- Log4j2 API

## License
This library is part of the SFN Common Modules project.
