# Usage Example

This document shows how to use the Common Cache Loader library in your Spring Boot application.

## 1. Add Dependency

```xml
<dependency>
    <groupId>com.sfn</groupId>
    <artifactId>common-cache-loader</artifactId>
    <version>1.4.0-SNAPSHOT</version>
</dependency>
```

## 2. Create Your Data Bean

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductBean {
    private String id;
    private String name;
    private String category;
    private boolean active;
    private BigDecimal price;
}
```

## 3. Implement Refresh Strategy

```java
@Component
public class ProductRefreshStrategy implements RefreshStrategy<ProductBean> {
    
    @Autowired
    private ProductRepository productRepository;
    
    @Override
    public List<ProductBean> loadData() throws Exception {
        try {
            // Load from database
            List<Product> products = productRepository.findAll();
            
            // Convert to beans
            return products.stream()
                .map(this::convertToBean)
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            log.error("Failed to load products", e);
            throw new Exception("Product data refresh failed", e);
        }
    }
    
    private ProductBean convertToBean(Product product) {
        return new ProductBean(
            product.getId(),
            product.getName(),
            product.getCategory(),
            product.isActive(),
            product.getPrice()
        );
    }
}
```

## 4. Create Service with Cache

```java
@Service
@RequiredArgsConstructor
public class ProductService {
    
    private final CacheManager<ProductBean> cacheManager;
    private final ProductRefreshStrategy productRefreshStrategy;
    
    private static final String PRODUCT_CACHE_KEY = "products";
    
    @PostConstruct
    public void initializeCache() {
        // Register refresh strategy
        cacheManager.registerRefreshStrategy(PRODUCT_CACHE_KEY, productRefreshStrategy);
        
        // Load initial data
        cacheManager.refresh(PRODUCT_CACHE_KEY);
    }
    
    public List<ProductBean> getAllProducts() {
        return cacheManager.get(PRODUCT_CACHE_KEY).orElse(List.of());
    }
    
    public List<ProductBean> getActiveProducts() {
        return getAllProducts().stream()
            .filter(ProductBean::isActive)
            .collect(Collectors.toList());
    }
    
    public Optional<ProductBean> findProductById(String id) {
        return getAllProducts().stream()
            .filter(p -> p.getId().equals(id))
            .findFirst();
    }
    
    public List<ProductBean> findProductsByCategory(String category) {
        return getAllProducts().stream()
            .filter(p -> p.getCategory().equalsIgnoreCase(category))
            .collect(Collectors.toList());
    }
    
    public boolean refreshProductCache() {
        return cacheManager.refresh(PRODUCT_CACHE_KEY);
    }
}
```

## 5. Configuration (Optional)

```yaml
# application.yml
sfn:
  cache:
    refresh-endpoints-enabled: true
    auto-initialize: true
    debug-logging: false
    max-cache-size: 1000
    fail-fast-on-init-error: false
```

## 6. Using in Controller

```java
@RestController
@RequestMapping("/api/products")
@RequiredArgsConstructor
public class ProductController {
    
    private final ProductService productService;
    
    @GetMapping
    public ResponseEntity<List<ProductBean>> getAllProducts() {
        List<ProductBean> products = productService.getAllProducts();
        return ResponseEntity.ok(products);
    }
    
    @GetMapping("/active")
    public ResponseEntity<List<ProductBean>> getActiveProducts() {
        List<ProductBean> products = productService.getActiveProducts();
        return ResponseEntity.ok(products);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<ProductBean> getProduct(@PathVariable String id) {
        return productService.findProductById(id)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping("/category/{category}")
    public ResponseEntity<List<ProductBean>> getProductsByCategory(@PathVariable String category) {
        List<ProductBean> products = productService.findProductsByCategory(category);
        return ResponseEntity.ok(products);
    }
    
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshCache() {
        boolean success = productService.refreshProductCache();
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        response.put("message", success ? "Cache refreshed successfully" : "Cache refresh failed");
        
        return ResponseEntity.ok(response);
    }
}
```

## 7. Multiple Cache Types

```java
@Service
@RequiredArgsConstructor
public class MultiCacheService {
    
    private final CacheManager<Object> cacheManager;
    private final UserRefreshStrategy userStrategy;
    private final ProductRefreshStrategy productStrategy;
    private final CategoryRefreshStrategy categoryStrategy;
    
    @PostConstruct
    public void initializeAllCaches() {
        // Register all strategies
        cacheManager.registerRefreshStrategy("users", userStrategy);
        cacheManager.registerRefreshStrategy("products", productStrategy);
        cacheManager.registerRefreshStrategy("categories", categoryStrategy);
        
        // Initialize all caches
        cacheManager.refreshAll();
    }
    
    @SuppressWarnings("unchecked")
    public List<UserBean> getUsers() {
        return (List<UserBean>) cacheManager.get("users").orElse(List.of());
    }
    
    @SuppressWarnings("unchecked")
    public List<ProductBean> getProducts() {
        return (List<ProductBean>) cacheManager.get("products").orElse(List.of());
    }
    
    @SuppressWarnings("unchecked")
    public List<CategoryBean> getCategories() {
        return (List<CategoryBean>) cacheManager.get("categories").orElse(List.of());
    }
}
```

## 8. Cache Management Endpoints

The library automatically provides REST endpoints for cache management:

```bash
# Refresh specific cache
curl -X POST http://localhost:8080/api/cache/refresh/products

# Refresh all caches
curl -X POST http://localhost:8080/api/cache/refresh-all

# Get cache status
curl -X GET http://localhost:8080/api/cache/status

# Clear specific cache
curl -X DELETE http://localhost:8080/api/cache/clear/products

# Clear all caches
curl -X DELETE http://localhost:8080/api/cache/clear-all
```

## 9. Error Handling

```java
@Service
public class RobustCacheService {
    
    @PostConstruct
    public void initializeCache() {
        try {
            cacheManager.registerRefreshStrategy("data", this::loadDataWithRetry);
            
            if (!cacheManager.refresh("data")) {
                log.warn("Initial cache load failed, will retry later");
                // Schedule retry or use fallback data
            }
        } catch (Exception e) {
            log.error("Cache initialization failed", e);
            // Handle initialization failure
        }
    }
    
    private List<DataBean> loadDataWithRetry() throws Exception {
        int maxRetries = 3;
        Exception lastException = null;
        
        for (int i = 0; i < maxRetries; i++) {
            try {
                return dataRepository.findAll();
            } catch (Exception e) {
                lastException = e;
                log.warn("Data load attempt {} failed", i + 1, e);
                
                if (i < maxRetries - 1) {
                    Thread.sleep(1000 * (i + 1)); // Exponential backoff
                }
            }
        }
        
        throw new Exception("Failed to load data after " + maxRetries + " attempts", lastException);
    }
}
```

## 10. Testing

```java
@ExtendWith(MockitoExtension.class)
class ProductServiceTest {
    
    @Mock
    private CacheManager<ProductBean> cacheManager;
    
    @Mock
    private ProductRefreshStrategy refreshStrategy;
    
    @InjectMocks
    private ProductService productService;
    
    @Test
    void testGetAllProducts() {
        // Given
        List<ProductBean> mockProducts = Arrays.asList(
            new ProductBean("1", "Product1", "Category1", true, BigDecimal.TEN)
        );
        when(cacheManager.get("products")).thenReturn(Optional.of(mockProducts));
        
        // When
        List<ProductBean> result = productService.getAllProducts();
        
        // Then
        assertEquals(1, result.size());
        assertEquals("Product1", result.get(0).getName());
    }
}
```

This example demonstrates the complete usage of the Common Cache Loader library in a real-world Spring Boot application.
