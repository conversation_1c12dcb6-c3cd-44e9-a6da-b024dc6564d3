package com.sfn.cache.impl;

import com.sfn.cache.RefreshStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class InMemoryCacheManagerTest {
    
    private InMemoryCacheManager<String> cacheManager;
    
    @BeforeEach
    void setUp() {
        cacheManager = new InMemoryCacheManager<>();
    }
    
    @Test
    void testPutAndGet() {
        // Given
        String key = "testKey";
        List<String> data = Arrays.asList("item1", "item2", "item3");
        
        // When
        cacheManager.put(key, data);
        Optional<List<String>> result = cacheManager.get(key);
        
        // Then
        assertTrue(result.isPresent());
        assertEquals(3, result.get().size());
        assertEquals("item1", result.get().get(0));
    }
    
    @Test
    void testGetNonExistentKey() {
        // When
        Optional<List<String>> result = cacheManager.get("nonExistent");
        
        // Then
        assertFalse(result.isPresent());
    }
    
    @Test
    void testPutWithNullKey() {
        // Given
        List<String> data = Arrays.asList("item1");
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            cacheManager.put(null, data);
        });
    }
    
    @Test
    void testPutWithNullData() {
        // Given
        String key = "testKey";
        
        // When
        cacheManager.put(key, null);
        Optional<List<String>> result = cacheManager.get(key);
        
        // Then
        assertTrue(result.isPresent());
        assertTrue(result.get().isEmpty());
    }
    
    @Test
    void testRefreshStrategy() {
        // Given
        String key = "testKey";
        RefreshStrategy<String> strategy = () -> Arrays.asList("refreshed1", "refreshed2");
        
        // When
        cacheManager.registerRefreshStrategy(key, strategy);
        boolean refreshResult = cacheManager.refresh(key);
        Optional<List<String>> result = cacheManager.get(key);
        
        // Then
        assertTrue(refreshResult);
        assertTrue(result.isPresent());
        assertEquals(2, result.get().size());
        assertEquals("refreshed1", result.get().get(0));
    }
    
    @Test
    void testRefreshWithoutStrategy() {
        // Given
        String key = "testKey";
        
        // When
        boolean refreshResult = cacheManager.refresh(key);
        
        // Then
        assertFalse(refreshResult);
    }
    
    @Test
    void testRefreshWithException() {
        // Given
        String key = "testKey";
        RefreshStrategy<String> strategy = () -> {
            throw new RuntimeException("Refresh failed");
        };
        
        // When
        cacheManager.registerRefreshStrategy(key, strategy);
        boolean refreshResult = cacheManager.refresh(key);
        
        // Then
        assertFalse(refreshResult);
    }
    
    @Test
    void testContainsKey() {
        // Given
        String key = "testKey";
        List<String> data = Arrays.asList("item1");
        
        // When
        cacheManager.put(key, data);
        
        // Then
        assertTrue(cacheManager.containsKey(key));
        assertFalse(cacheManager.containsKey("nonExistent"));
    }
    
    @Test
    void testRemove() {
        // Given
        String key = "testKey";
        List<String> data = Arrays.asList("item1");
        cacheManager.put(key, data);
        
        // When
        boolean removed = cacheManager.remove(key);
        
        // Then
        assertTrue(removed);
        assertFalse(cacheManager.containsKey(key));
        assertFalse(cacheManager.remove("nonExistent"));
    }
    
    @Test
    void testClear() {
        // Given
        cacheManager.put("key1", Arrays.asList("item1"));
        cacheManager.put("key2", Arrays.asList("item2"));
        
        // When
        cacheManager.clear();
        
        // Then
        assertEquals(0, cacheManager.size());
        assertFalse(cacheManager.containsKey("key1"));
        assertFalse(cacheManager.containsKey("key2"));
    }
    
    @Test
    void testSize() {
        // Given
        assertEquals(0, cacheManager.size());
        
        // When
        cacheManager.put("key1", Arrays.asList("item1"));
        cacheManager.put("key2", Arrays.asList("item2"));
        
        // Then
        assertEquals(2, cacheManager.size());
    }
    
    @Test
    void testGetKeys() {
        // Given
        cacheManager.put("key1", Arrays.asList("item1"));
        cacheManager.put("key2", Arrays.asList("item2"));
        
        // When
        List<String> keys = cacheManager.getKeys();
        
        // Then
        assertEquals(2, keys.size());
        assertTrue(keys.contains("key1"));
        assertTrue(keys.contains("key2"));
    }
    
    @Test
    void testRefreshAll() {
        // Given
        RefreshStrategy<String> strategy1 = () -> Arrays.asList("data1");
        RefreshStrategy<String> strategy2 = () -> Arrays.asList("data2");
        
        cacheManager.registerRefreshStrategy("key1", strategy1);
        cacheManager.registerRefreshStrategy("key2", strategy2);
        
        // When
        int refreshCount = cacheManager.refreshAll();
        
        // Then
        assertEquals(2, refreshCount);
        assertTrue(cacheManager.containsKey("key1"));
        assertTrue(cacheManager.containsKey("key2"));
    }
    
    @Test
    void testDefensiveCopy() {
        // Given
        String key = "testKey";
        List<String> originalData = Arrays.asList("item1", "item2");
        
        // When
        cacheManager.put(key, originalData);
        Optional<List<String>> cachedData = cacheManager.get(key);
        
        // Then
        assertTrue(cachedData.isPresent());
        assertNotSame(originalData, cachedData.get()); // Should be different objects
        assertEquals(originalData, cachedData.get()); // But same content
    }
}
