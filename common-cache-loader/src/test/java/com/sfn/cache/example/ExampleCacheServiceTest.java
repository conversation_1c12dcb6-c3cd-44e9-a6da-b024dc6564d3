package com.sfn.cache.example;

import com.sfn.cache.CacheManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ExampleCacheServiceTest {
    
    @Mock
    private CacheManager<AcquirerBean> cacheManager;
    
    @Mock
    private AcquirerRefreshStrategy acquirerRefreshStrategy;
    
    private ExampleCacheService exampleCacheService;
    
    @BeforeEach
    void setUp() {
        exampleCacheService = new ExampleCacheService(cacheManager, acquirerRefreshStrategy);
    }
    
    @Test
    void testInitializeCache() {
        // Given
        when(cacheManager.refresh("acquirer")).thenReturn(true);
        
        // When
        exampleCacheService.initializeCache();
        
        // Then
        verify(cacheManager).registerRefreshStrategy(eq("acquirer"), eq(acquirerRefreshStrategy));
        verify(cacheManager).refresh("acquirer");
    }
    
    @Test
    void testGetAllAcquirers() {
        // Given
        List<AcquirerBean> mockData = Arrays.asList(
            new AcquirerBean("Bank1", "url1"),
            new AcquirerBean("Bank2", "url2")
        );
        when(cacheManager.get("acquirer")).thenReturn(Optional.of(mockData));
        
        // When
        List<AcquirerBean> result = exampleCacheService.getAllAcquirers();
        
        // Then
        assertEquals(2, result.size());
        assertEquals("Bank1", result.get(0).getName());
        verify(cacheManager).get("acquirer");
    }
    
    @Test
    void testGetAllAcquirersWhenCacheEmpty() {
        // Given
        when(cacheManager.get("acquirer")).thenReturn(Optional.empty());
        
        // When
        List<AcquirerBean> result = exampleCacheService.getAllAcquirers();
        
        // Then
        assertTrue(result.isEmpty());
        verify(cacheManager).get("acquirer");
    }
    
    @Test
    void testGetActiveAcquirers() {
        // Given
        List<AcquirerBean> mockData = Arrays.asList(
            new AcquirerBean("1", "Bank1", "url1", true, "Active bank"),
            new AcquirerBean("2", "Bank2", "url2", false, "Inactive bank"),
            new AcquirerBean("3", "Bank3", "url3", true, "Active bank")
        );
        when(cacheManager.get("acquirer")).thenReturn(Optional.of(mockData));
        
        // When
        List<AcquirerBean> result = exampleCacheService.getActiveAcquirers();
        
        // Then
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(AcquirerBean::isActive));
    }
    
    @Test
    void testFindAcquirerByName() {
        // Given
        List<AcquirerBean> mockData = Arrays.asList(
            new AcquirerBean("Bank1", "url1"),
            new AcquirerBean("Bank2", "url2")
        );
        when(cacheManager.get("acquirer")).thenReturn(Optional.of(mockData));
        
        // When
        Optional<AcquirerBean> result = exampleCacheService.findAcquirerByName("Bank1");
        
        // Then
        assertTrue(result.isPresent());
        assertEquals("Bank1", result.get().getName());
    }
    
    @Test
    void testFindAcquirerByNameNotFound() {
        // Given
        List<AcquirerBean> mockData = Arrays.asList(
            new AcquirerBean("Bank1", "url1")
        );
        when(cacheManager.get("acquirer")).thenReturn(Optional.of(mockData));
        
        // When
        Optional<AcquirerBean> result = exampleCacheService.findAcquirerByName("NonExistent");
        
        // Then
        assertFalse(result.isPresent());
    }
    
    @Test
    void testRefreshAcquirerCache() {
        // Given
        when(cacheManager.refresh("acquirer")).thenReturn(true);
        
        // When
        boolean result = exampleCacheService.refreshAcquirerCache();
        
        // Then
        assertTrue(result);
        verify(cacheManager).refresh("acquirer");
    }
    
    @Test
    void testIsAcquirerCachePopulated() {
        // Given
        List<AcquirerBean> mockData = Arrays.asList(new AcquirerBean("Bank1", "url1"));
        when(cacheManager.containsKey("acquirer")).thenReturn(true);
        when(cacheManager.get("acquirer")).thenReturn(Optional.of(mockData));
        
        // When
        boolean result = exampleCacheService.isAcquirerCachePopulated();
        
        // Then
        assertTrue(result);
    }
    
    @Test
    void testIsAcquirerCachePopulatedWhenEmpty() {
        // Given
        when(cacheManager.containsKey("acquirer")).thenReturn(true);
        when(cacheManager.get("acquirer")).thenReturn(Optional.of(List.of()));
        
        // When
        boolean result = exampleCacheService.isAcquirerCachePopulated();
        
        // Then
        assertFalse(result);
    }
    
    @Test
    void testGetCacheStats() {
        // Given
        List<AcquirerBean> mockData = Arrays.asList(
            new AcquirerBean("1", "Bank1", "url1", true, "Active"),
            new AcquirerBean("2", "Bank2", "url2", false, "Inactive")
        );
        when(cacheManager.containsKey("acquirer")).thenReturn(true);
        when(cacheManager.get("acquirer")).thenReturn(Optional.of(mockData));
        
        // When
        String stats = exampleCacheService.getCacheStats();
        
        // Then
        assertEquals("Acquirer Cache - Total: 2, Active: 1", stats);
    }
    
    @Test
    void testGetCacheStatsWhenNotInitialized() {
        // Given
        when(cacheManager.containsKey("acquirer")).thenReturn(false);
        
        // When
        String stats = exampleCacheService.getCacheStats();
        
        // Then
        assertEquals("Acquirer Cache - Not initialized", stats);
    }
}
