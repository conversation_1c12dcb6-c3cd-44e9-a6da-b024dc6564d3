package com.sfn.cache.controller;

import com.sfn.cache.CacheManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST Controller for cache refresh operations.
 * Provides endpoints to refresh individual caches or all caches.
 */
@Log4j2
@RestController
@RequestMapping("/api/cache")
@RequiredArgsConstructor
public class CacheRefreshController {
    
    private final CacheManager<?> cacheManager;
    
    /**
     * Refresh a specific cache by key
     *
     * @param key The cache key to refresh
     * @return Response indicating success or failure
     */
    @PostMapping("/refresh/{key}")
    public ResponseEntity<Map<String, Object>> refreshCache(@PathVariable String key) {
        log.info("Received request to refresh cache for key: {}", key);
        
        Map<String, Object> response = new HashMap<>();
        
        if (!cacheManager.containsKey(key)) {
            response.put("success", false);
            response.put("message", "Cache key not found: " + key);
            response.put("key", key);
            log.warn("Cache key not found: {}", key);
            return ResponseEntity.badRequest().body(response);
        }
        
        boolean success = cacheManager.refresh(key);
        
        response.put("success", success);
        response.put("key", key);
        response.put("message", success ? "Cache refreshed successfully" : "Failed to refresh cache");
        
        if (success) {
            log.info("Successfully refreshed cache for key: {}", key);
            return ResponseEntity.ok(response);
        } else {
            log.error("Failed to refresh cache for key: {}", key);
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * Refresh all caches
     *
     * @return Response with refresh results
     */
    @PostMapping("/refresh-all")
    public ResponseEntity<Map<String, Object>> refreshAllCaches() {
        log.info("Received request to refresh all caches");
        
        int totalCaches = cacheManager.size();
        int successCount = cacheManager.refreshAll();
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", successCount > 0);
        response.put("totalCaches", totalCaches);
        response.put("successCount", successCount);
        response.put("failureCount", totalCaches - successCount);
        response.put("message", String.format("Refreshed %d out of %d caches", successCount, totalCaches));
        
        log.info("Refresh all caches completed. Success: {}/{}", successCount, totalCaches);
        return ResponseEntity.ok(response);
    }
    
    /**
     * Get cache status and information
     *
     * @return Cache status information
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getCacheStatus() {
        Map<String, Object> status = new HashMap<>();
        
        List<String> keys = cacheManager.getKeys();
        status.put("totalCaches", cacheManager.size());
        status.put("cacheKeys", keys);
        
        Map<String, Integer> cacheSizes = new HashMap<>();
        for (String key : keys) {
            cacheManager.get(key).ifPresent(data -> cacheSizes.put(key, data.size()));
        }
        status.put("cacheSizes", cacheSizes);
        
        return ResponseEntity.ok(status);
    }
    
    /**
     * Clear a specific cache
     *
     * @param key The cache key to clear
     * @return Response indicating success or failure
     */
    @DeleteMapping("/clear/{key}")
    public ResponseEntity<Map<String, Object>> clearCache(@PathVariable String key) {
        log.info("Received request to clear cache for key: {}", key);
        
        Map<String, Object> response = new HashMap<>();
        boolean removed = cacheManager.remove(key);
        
        response.put("success", removed);
        response.put("key", key);
        response.put("message", removed ? "Cache cleared successfully" : "Cache key not found");
        
        if (removed) {
            log.info("Successfully cleared cache for key: {}", key);
            return ResponseEntity.ok(response);
        } else {
            log.warn("Cache key not found for clearing: {}", key);
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Clear all caches
     *
     * @return Response indicating success
     */
    @DeleteMapping("/clear-all")
    public ResponseEntity<Map<String, Object>> clearAllCaches() {
        log.info("Received request to clear all caches");
        
        int totalCaches = cacheManager.size();
        cacheManager.clear();
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("clearedCount", totalCaches);
        response.put("message", "All caches cleared successfully");
        
        log.info("Successfully cleared all {} caches", totalCaches);
        return ResponseEntity.ok(response);
    }
}
