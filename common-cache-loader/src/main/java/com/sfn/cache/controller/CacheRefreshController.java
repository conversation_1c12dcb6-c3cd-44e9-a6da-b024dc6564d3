package com.sfn.cache.controller;

import com.sfn.cache.UnifiedCacheManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST Controller for cache refresh operations.
 * Provides endpoints to refresh individual caches or all caches.
 * Supports both list and map cache operations.
 */
@Log4j2
@RestController
@RequestMapping("/api/cache")
@RequiredArgsConstructor
public class CacheRefreshController {

    private final UnifiedCacheManager<Object, String, String> cacheManager;
    
    /**
     * Refresh a specific cache by key
     *
     * @param key The cache key to refresh
     * @return Response indicating success or failure
     */
    @PostMapping("/refresh/{key}")
    public ResponseEntity<Map<String, Object>> refreshCache(@PathVariable String key) {
        log.info("Received request to refresh cache for key: {}", key);
        
        Map<String, Object> response = new HashMap<>();
        
        if (!cacheManager.containsKey(key)) {
            response.put("success", false);
            response.put("message", "Cache key not found: " + key);
            response.put("key", key);
            log.warn("Cache key not found: {}", key);
            return ResponseEntity.badRequest().body(response);
        }
        
        boolean success = cacheManager.refresh(key);
        
        response.put("success", success);
        response.put("key", key);
        response.put("message", success ? "Cache refreshed successfully" : "Failed to refresh cache");
        
        if (success) {
            log.info("Successfully refreshed cache for key: {}", key);
            return ResponseEntity.ok(response);
        } else {
            log.error("Failed to refresh cache for key: {}", key);
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * Refresh all caches
     *
     * @return Response with refresh results
     */
    @PostMapping("/refresh-all")
    public ResponseEntity<Map<String, Object>> refreshAllCaches() {
        log.info("Received request to refresh all caches");
        
        int totalCaches = cacheManager.size();
        int successCount = cacheManager.refreshAll();
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", successCount > 0);
        response.put("totalCaches", totalCaches);
        response.put("successCount", successCount);
        response.put("failureCount", totalCaches - successCount);
        response.put("message", String.format("Refreshed %d out of %d caches", successCount, totalCaches));
        
        log.info("Refresh all caches completed. Success: {}/{}", successCount, totalCaches);
        return ResponseEntity.ok(response);
    }
    
    /**
     * Get cache status and information
     *
     * @return Cache status information
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getCacheStatus() {
        Map<String, Object> status = cacheManager.getCacheStatistics();
        return ResponseEntity.ok(status);
    }
    
    /**
     * Clear a specific cache
     *
     * @param key The cache key to clear
     * @return Response indicating success or failure
     */
    @DeleteMapping("/clear/{key}")
    public ResponseEntity<Map<String, Object>> clearCache(@PathVariable String key) {
        log.info("Received request to clear cache for key: {}", key);
        
        Map<String, Object> response = new HashMap<>();
        boolean removed = cacheManager.remove(key);
        
        response.put("success", removed);
        response.put("key", key);
        response.put("message", removed ? "Cache cleared successfully" : "Cache key not found");
        
        if (removed) {
            log.info("Successfully cleared cache for key: {}", key);
            return ResponseEntity.ok(response);
        } else {
            log.warn("Cache key not found for clearing: {}", key);
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * Clear all caches
     *
     * @return Response indicating success
     */
    @DeleteMapping("/clear-all")
    public ResponseEntity<Map<String, Object>> clearAllCaches() {
        log.info("Received request to clear all caches");

        int totalCaches = cacheManager.getTotalCacheCount();
        cacheManager.clearAllCaches();

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("clearedCount", totalCaches);
        response.put("message", "All caches cleared successfully");

        log.info("Successfully cleared all {} caches", totalCaches);
        return ResponseEntity.ok(response);
    }

    // Map Cache Endpoints

    /**
     * Refresh a specific map cache by key
     *
     * @param key The map cache key to refresh
     * @return Response indicating success or failure
     */
    @PostMapping("/map/refresh/{key}")
    public ResponseEntity<Map<String, Object>> refreshMapCache(@PathVariable String key) {
        log.info("Received request to refresh map cache for key: {}", key);

        Map<String, Object> response = new HashMap<>();

        if (!cacheManager.containsMapKey(key)) {
            response.put("success", false);
            response.put("message", "Map cache key not found: " + key);
            response.put("key", key);
            log.warn("Map cache key not found: {}", key);
            return ResponseEntity.badRequest().body(response);
        }

        boolean success = cacheManager.refreshMap(key);

        response.put("success", success);
        response.put("key", key);
        response.put("message", success ? "Map cache refreshed successfully" : "Failed to refresh map cache");

        if (success) {
            log.info("Successfully refreshed map cache for key: {}", key);
            return ResponseEntity.ok(response);
        } else {
            log.error("Failed to refresh map cache for key: {}", key);
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Get a specific value from a map cache
     *
     * @param cacheKey The cache key
     * @param mapKey   The key within the map
     * @return Response with the value or error
     */
    @GetMapping("/map/{cacheKey}/{mapKey}")
    public ResponseEntity<Map<String, Object>> getMapValue(
            @PathVariable String cacheKey,
            @PathVariable String mapKey) {

        Optional<String> value = cacheManager.getMapValue(cacheKey, mapKey);

        Map<String, Object> response = new HashMap<>();
        response.put("cacheKey", cacheKey);
        response.put("mapKey", mapKey);

        if (value.isPresent()) {
            response.put("success", true);
            response.put("value", value.get());
            return ResponseEntity.ok(response);
        } else {
            response.put("success", false);
            response.put("message", "Value not found");
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Update a specific value in a map cache
     *
     * @param cacheKey The cache key
     * @param mapKey   The key within the map
     * @param request  Request body containing the new value
     * @return Response indicating success or failure
     */
    @PutMapping("/map/{cacheKey}/{mapKey}")
    public ResponseEntity<Map<String, Object>> updateMapValue(
            @PathVariable String cacheKey,
            @PathVariable String mapKey,
            @RequestBody Map<String, String> request) {

        String value = request.get("value");
        if (value == null) {
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", "Value is required in request body"
            ));
        }

        boolean success = cacheManager.putMapValue(cacheKey, mapKey, value);

        Map<String, Object> response = new HashMap<>();
        response.put("success", success);
        response.put("cacheKey", cacheKey);
        response.put("mapKey", mapKey);
        response.put("message", success ? "Value updated successfully" : "Failed to update value");

        if (success) {
            log.info("Updated map value for cache key: {}, map key: {}", cacheKey, mapKey);
            return ResponseEntity.ok(response);
        } else {
            log.warn("Failed to update map value for cache key: {}, map key: {}", cacheKey, mapKey);
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Clear a specific map cache
     *
     * @param key The map cache key to clear
     * @return Response indicating success or failure
     */
    @DeleteMapping("/map/clear/{key}")
    public ResponseEntity<Map<String, Object>> clearMapCache(@PathVariable String key) {
        log.info("Received request to clear map cache for key: {}", key);

        boolean removed = cacheManager.removeMap(key);

        Map<String, Object> response = new HashMap<>();
        response.put("success", removed);
        response.put("key", key);
        response.put("message", removed ? "Map cache cleared successfully" : "Map cache key not found");

        if (removed) {
            log.info("Successfully cleared map cache for key: {}", key);
            return ResponseEntity.ok(response);
        } else {
            log.warn("Map cache key not found for clearing: {}", key);
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Refresh all caches (both list and map when available)
     *
     * @return Response with comprehensive refresh results
     */
    @PostMapping("/refresh-all-unified")
    public ResponseEntity<Map<String, Object>> refreshAllUnifiedCaches() {
        log.info("Received request to refresh all unified caches");

        Map<String, Integer> refreshResults = cacheManager.refreshAllCaches();

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.putAll(refreshResults);
        response.put("message", String.format(
            "Refreshed %d total caches (%d list, %d map)",
            refreshResults.get("totalRefreshCount"),
            refreshResults.get("listCacheRefreshCount"),
            refreshResults.get("mapCacheRefreshCount")
        ));

        log.info("Refresh all unified caches completed. Results: {}", refreshResults);
        return ResponseEntity.ok(response);
    }
}
