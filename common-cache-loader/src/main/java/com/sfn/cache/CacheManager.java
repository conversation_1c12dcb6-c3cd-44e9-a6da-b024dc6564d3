package com.sfn.cache;

import java.util.List;
import java.util.Optional;

/**
 * Core interface for cache management operations.
 * Provides methods to store, retrieve, and manage cached data with refresh strategies.
 *
 * @param <T> The type of objects stored in the cache
 */
public interface CacheManager<T> {
    
    /**
     * Store data in cache with the specified key
     *
     * @param key   The cache key
     * @param data  The data to cache
     */
    void put(String key, List<T> data);
    
    /**
     * Retrieve data from cache by key
     *
     * @param key The cache key
     * @return Optional containing the cached data, or empty if not found
     */
    Optional<List<T>> get(String key);
    
    /**
     * Register a refresh strategy for a specific cache key
     *
     * @param key             The cache key
     * @param refreshStrategy The refresh strategy to use
     */
    void registerRefreshStrategy(String key, RefreshStrategy<T> refreshStrategy);
    
    /**
     * Refresh cache data for a specific key using its registered refresh strategy
     *
     * @param key The cache key to refresh
     * @return true if refresh was successful, false otherwise
     */
    boolean refresh(String key);
    
    /**
     * Refresh all caches that have registered refresh strategies
     *
     * @return Number of successfully refreshed caches
     */
    int refreshAll();
    
    /**
     * Check if a key exists in the cache
     *
     * @param key The cache key
     * @return true if key exists, false otherwise
     */
    boolean containsKey(String key);
    
    /**
     * Remove data from cache
     *
     * @param key The cache key to remove
     * @return true if key was removed, false if key didn't exist
     */
    boolean remove(String key);
    
    /**
     * Clear all cached data
     */
    void clear();
    
    /**
     * Get the size of the cache
     *
     * @return Number of cached entries
     */
    int size();
    
    /**
     * Get all cache keys
     *
     * @return List of all cache keys
     */
    List<String> getKeys();
}
