package com.sfn.cache.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark methods that should be called during cache initialization.
 * Methods annotated with this will be automatically invoked after Spring context initialization.
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CacheInitializer {
    
    /**
     * Priority order for initialization (lower values have higher priority)
     */
    int order() default 0;
    
    /**
     * Whether to continue initialization if this method fails
     */
    boolean continueOnError() default true;
}
