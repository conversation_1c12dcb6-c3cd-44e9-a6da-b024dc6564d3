package com.sfn.cache;

import java.util.Map;

/**
 * Strategy interface for refreshing map cache data.
 * Implementations define how to load/reload Map<K, V> data for specific cache entries.
 *
 * @param <K> The type of keys in the map
 * @param <V> The type of values in the map
 */
@FunctionalInterface
public interface MapRefreshStrategy<K, V> {

    /**
     * Load map data for the cache.
     * This method should implement the logic to fetch fresh map data from the data source.
     *
     * @return Map<K, V> data to be cached
     * @throws Exception if data loading fails
     */
    Map<K, V> loadMapData() throws Exception;
}
