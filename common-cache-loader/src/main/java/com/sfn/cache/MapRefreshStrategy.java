package com.sfn.cache;

import java.util.Map;

/**
 * Strategy interface for loading map data.
 * Implementations define how to refresh cached map data.
 *
 * @param <K> The type of keys in the map
 * @param <V> The type of values in the map
 */
@FunctionalInterface
public interface MapRefreshStrategy<K, V> {
    
    /**
     * Load map data for caching
     *
     * @return Map data to be cached
     * @throws Exception if data loading fails
     */
    Map<K, V> loadMapData() throws Exception;
}
