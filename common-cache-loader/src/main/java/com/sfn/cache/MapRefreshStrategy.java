package com.sfn.cache;

import java.util.Map;

/**
 * Strategy interface for refreshing map cache data.
 * Implementations define how to load/reload Map<String, String> data for specific cache entries.
 */
@FunctionalInterface
public interface MapRefreshStrategy {

    /**
     * Load map data for the cache.
     * This method should implement the logic to fetch fresh map data from the data source.
     *
     * @return Map<String, String> data to be cached
     * @throws Exception if data loading fails
     */
    Map<String, String> loadMapData() throws Exception;
}
