package com.sfn.cache;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Cache manager interface for Map<String, String> data.
 * Provides methods to store, retrieve, and manage cached map data with refresh strategies.
 */
public interface MapCacheManager {
    
    /**
     * Store map data in cache with the specified key
     *
     * @param key   The cache key
     * @param data  The map data to cache
     */
    void putMap(String key, Map<K, V> data);
    
    /**
     * Retrieve map data from cache by key
     *
     * @param key The cache key
     * @return Optional containing the cached map data, or empty if not found
     */
    Optional<Map<K, V>> getMap(String key);
    
    /**
     * Get a specific value from a cached map
     *
     * @param cacheKey The cache key
     * @param mapKey   The key within the map
     * @return Optional containing the value, or empty if not found
     */
    Optional<V> getMapValue(String cacheKey, K mapKey);
    
    /**
     * Put a single key-value pair into a cached map
     *
     * @param cacheKey The cache key
     * @param mapKey   The key within the map
     * @param value    The value to store
     * @return true if the operation was successful, false if cache key doesn't exist
     */
    boolean putMapValue(String cacheKey, K mapKey, V value);
    
    /**
     * Remove a key from a cached map
     *
     * @param cacheKey The cache key
     * @param mapKey   The key within the map to remove
     * @return true if the key was removed, false if not found
     */
    boolean removeMapValue(String cacheKey, K mapKey);
    
    /**
     * Register a refresh strategy for a specific cache key
     *
     * @param key             The cache key
     * @param refreshStrategy The refresh strategy to use
     */
    void registerMapRefreshStrategy(String key, MapRefreshStrategy<K, V> refreshStrategy);
    
    /**
     * Refresh map cache data for a specific key using its registered refresh strategy
     *
     * @param key The cache key to refresh
     * @return true if refresh was successful, false otherwise
     */
    boolean refreshMap(String key);
    
    /**
     * Refresh all map caches that have registered refresh strategies
     *
     * @return Number of successfully refreshed caches
     */
    int refreshAllMaps();
    
    /**
     * Check if a map cache key exists
     *
     * @param key The cache key
     * @return true if key exists, false otherwise
     */
    boolean containsMapKey(String key);
    
    /**
     * Check if a specific key exists within a cached map
     *
     * @param cacheKey The cache key
     * @param mapKey   The key within the map
     * @return true if the map key exists, false otherwise
     */
    boolean containsMapValue(String cacheKey, K mapKey);
    
    /**
     * Remove map data from cache
     *
     * @param key The cache key to remove
     * @return true if key was removed, false if key didn't exist
     */
    boolean removeMap(String key);
    
    /**
     * Clear all cached map data
     */
    void clearMaps();
    
    /**
     * Get the number of cached maps
     *
     * @return Number of cached map entries
     */
    int mapCacheSize();
    
    /**
     * Get all map cache keys
     *
     * @return List of all map cache keys
     */
    List<String> getMapKeys();
    
    /**
     * Get the size of a specific cached map
     *
     * @param cacheKey The cache key
     * @return Size of the map, or 0 if not found
     */
    int getMapSize(String cacheKey);
}
