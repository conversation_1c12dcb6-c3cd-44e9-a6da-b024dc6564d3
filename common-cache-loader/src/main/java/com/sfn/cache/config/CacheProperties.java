package com.sfn.cache.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for cache management.
 * Allows customization of cache behavior through application properties.
 */
@Data
@Component
@ConfigurationProperties(prefix = "sfn.cache")
public class CacheProperties {
    
    /**
     * Whether cache refresh endpoints are enabled
     */
    private boolean refreshEndpointsEnabled = true;
    
    /**
     * Whether to enable automatic cache initialization on startup
     */
    private boolean autoInitialize = true;
    
    /**
     * Whether to log cache operations at debug level
     */
    private boolean debugLogging = false;
    
    /**
     * Maximum number of cache entries allowed
     */
    private int maxCacheSize = 1000;
    
    /**
     * Default cache refresh interval in minutes (0 = no automatic refresh)
     */
    private long defaultRefreshIntervalMinutes = 0;
    
    /**
     * Whether to fail fast on cache initialization errors
     */
    private boolean failFastOnInitError = false;
}
