package com.sfn.cache.config;

import com.sfn.cache.CacheManager;
import com.sfn.cache.controller.CacheRefreshController;
import com.sfn.cache.impl.InMemoryCacheManager;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Auto-configuration for cache management components.
 * Automatically configures cache manager and controller beans when not already present.
 */
@Log4j2
@Configuration
@EnableConfigurationProperties(CacheProperties.class)
public class CacheAutoConfiguration {
    
    /**
     * Creates a default in-memory cache manager if none is already configured
     *
     * @return InMemoryCacheManager instance
     */
    @Bean
    @ConditionalOnMissingBean(CacheManager.class)
    public CacheManager<?> cacheManager() {
        log.info("Creating default InMemoryCacheManager");
        return new InMemoryCacheManager<>();
    }
    
    /**
     * Creates cache refresh controller if refresh endpoints are enabled
     *
     * @param cacheManager The cache manager to use
     * @return CacheRefreshController instance
     */
    @Bean
    @ConditionalOnProperty(name = "sfn.cache.refresh-endpoints-enabled", havingValue = "true", matchIfMissing = true)
    public CacheRefreshController cacheRefreshController(CacheManager<?> cacheManager) {
        log.info("Creating CacheRefreshController with refresh endpoints enabled");
        return new CacheRefreshController(cacheManager);
    }
}
