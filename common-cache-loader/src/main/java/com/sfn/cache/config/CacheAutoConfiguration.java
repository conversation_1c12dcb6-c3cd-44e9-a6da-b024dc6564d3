package com.sfn.cache.config;

import com.sfn.cache.UnifiedCacheManager;
import com.sfn.cache.controller.CacheRefreshController;
import com.sfn.cache.impl.InMemoryUnifiedCacheManager;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Auto-configuration for cache management components.
 * Automatically configures unified cache manager and controller beans.
 */
@Log4j2
@Configuration
@EnableConfigurationProperties(CacheProperties.class)
public class CacheAutoConfiguration {

    /**
     * Creates a unified cache manager that supports both list and map caching
     * Uses Object types for maximum flexibility - users can cast as needed
     *
     * @return InMemoryUnifiedCacheManager instance
     */
    @Bean
    @ConditionalOnMissingBean(UnifiedCacheManager.class)
    public UnifiedCacheManager<Object, String, String> unifiedCacheManager() {
        log.info("Creating InMemoryUnifiedCacheManager with both list and map support");
        return new InMemoryUnifiedCacheManager<>();
    }

    /**
     * Creates cache refresh controller if refresh endpoints are enabled
     *
     * @param unifiedCacheManager The unified cache manager
     * @return CacheRefreshController instance
     */
    @Bean
    @ConditionalOnProperty(name = "sfn.cache.refresh-endpoints-enabled", havingValue = "true", matchIfMissing = true)
    public CacheRefreshController cacheRefreshController(UnifiedCacheManager<Object, String, String> unifiedCacheManager) {
        log.info("Creating CacheRefreshController with unified cache support");
        return new CacheRefreshController(unifiedCacheManager);
    }
}
