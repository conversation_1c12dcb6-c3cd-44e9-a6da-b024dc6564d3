package com.sfn.cache.config;

import com.sfn.cache.CacheManager;
import com.sfn.cache.MapCacheManager;
import com.sfn.cache.UnifiedCacheManager;
import com.sfn.cache.controller.CacheRefreshController;
import com.sfn.cache.impl.InMemoryCacheManager;
import com.sfn.cache.impl.InMemoryMapCacheManager;
import com.sfn.cache.impl.InMemoryUnifiedCacheManager;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Optional;

/**
 * Auto-configuration for cache management components.
 * Automatically configures cache manager and controller beans when not already present.
 * Supports both list and map caching through unified cache manager.
 */
@Log4j2
@Configuration
@EnableConfigurationProperties(CacheProperties.class)
public class CacheAutoConfiguration {

    /**
     * Creates a unified cache manager that supports both list and map caching
     * This is the primary cache manager that implements all cache interfaces
     *
     * @return InMemoryUnifiedCacheManager instance
     */
    @Bean
    @ConditionalOnMissingBean({CacheManager.class, MapCacheManager.class, UnifiedCacheManager.class})
    public UnifiedCacheManager<?, ?, ?> unifiedCacheManager() {
        log.info("Creating default InMemoryUnifiedCacheManager with both list and map support");
        return new InMemoryUnifiedCacheManager<>();
    }

    /**
     * Creates cache refresh controller if refresh endpoints are enabled
     *
     * @param cacheManager        The cache manager to use
     * @param unifiedCacheManager Optional unified cache manager for enhanced functionality
     * @return CacheRefreshController instance
     */
    @Bean
    @ConditionalOnProperty(name = "sfn.cache.refresh-endpoints-enabled", havingValue = "true", matchIfMissing = true)
    public CacheRefreshController cacheRefreshController(
            CacheManager<?> cacheManager,
            Optional<UnifiedCacheManager<?>> unifiedCacheManager) {

        if (unifiedCacheManager.isPresent()) {
            log.info("Creating CacheRefreshController with unified cache support");
        } else {
            log.info("Creating CacheRefreshController with basic cache support");
        }

        return new CacheRefreshController(cacheManager, unifiedCacheManager);
    }
}
