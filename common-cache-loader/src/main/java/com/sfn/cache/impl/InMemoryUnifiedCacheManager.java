package com.sfn.cache.impl;

import com.sfn.cache.UnifiedCacheManager;
import com.sfn.cache.RefreshStrategy;
import com.sfn.cache.MapRefreshStrategy;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Unified cache manager implementation that combines both list and map caching functionality.
 * Delegates to separate implementations for thread safety and separation of concerns.
 *
 * @param <T> The type of objects stored in list caches
 * @param <K> The type of keys in the map
 * @param <V> The type of values in the map
 */
@Log4j2
@Component
public class InMemoryUnifiedCacheManager<T, K, V> implements UnifiedCacheManager<T, K, V> {
    
    private final InMemoryCacheManager<T> listCacheManager;
    private final InMemoryMapCacheManager<K, V> mapCacheManager;
    
    public InMemoryUnifiedCacheManager() {
        this.listCacheManager = new InMemoryCacheManager<>();
        this.mapCacheManager = new InMemoryMapCacheManager<>();
    }
    
    // List Cache Manager methods delegation
    @Override
    public void put(String key, List<T> data) {
        listCacheManager.put(key, data);
    }
    
    @Override
    public Optional<List<T>> get(String key) {
        return listCacheManager.get(key);
    }
    
    @Override
    public void registerRefreshStrategy(String key, RefreshStrategy<T> refreshStrategy) {
        listCacheManager.registerRefreshStrategy(key, refreshStrategy);
    }
    
    @Override
    public boolean refresh(String key) {
        return listCacheManager.refresh(key);
    }
    
    @Override
    public int refreshAll() {
        return listCacheManager.refreshAll();
    }
    
    @Override
    public boolean containsKey(String key) {
        return listCacheManager.containsKey(key);
    }
    
    @Override
    public boolean remove(String key) {
        return listCacheManager.remove(key);
    }
    
    @Override
    public void clear() {
        listCacheManager.clear();
    }
    
    @Override
    public int size() {
        return listCacheManager.size();
    }
    
    @Override
    public List<String> getKeys() {
        return listCacheManager.getKeys();
    }
    
    // Map Cache Manager methods delegation
    @Override
    public void putMap(String key, Map<K, V> data) {
        mapCacheManager.putMap(key, data);
    }
    
    @Override
    public Optional<Map<K, V>> getMap(String key) {
        return mapCacheManager.getMap(key);
    }
    
    @Override
    public Optional<V> getMapValue(String cacheKey, K mapKey) {
        return mapCacheManager.getMapValue(cacheKey, mapKey);
    }
    
    @Override
    public boolean putMapValue(String cacheKey, K mapKey, V value) {
        return mapCacheManager.putMapValue(cacheKey, mapKey, value);
    }
    
    @Override
    public boolean removeMapValue(String cacheKey, K mapKey) {
        return mapCacheManager.removeMapValue(cacheKey, mapKey);
    }
    
    @Override
    public void registerMapRefreshStrategy(String key, MapRefreshStrategy<K, V> refreshStrategy) {
        mapCacheManager.registerMapRefreshStrategy(key, refreshStrategy);
    }
    
    @Override
    public boolean refreshMap(String key) {
        return mapCacheManager.refreshMap(key);
    }
    
    @Override
    public int refreshAllMaps() {
        return mapCacheManager.refreshAllMaps();
    }
    
    @Override
    public boolean containsMapKey(String key) {
        return mapCacheManager.containsMapKey(key);
    }

    @Override
    public boolean containsMapValue(String cacheKey, K mapKey) {
        return mapCacheManager.containsMapValue(cacheKey, mapKey);
    }

    @Override
    public boolean removeMap(String key) {
        return mapCacheManager.removeMap(key);
    }
    
    @Override
    public void clearMaps() {
        mapCacheManager.clearMaps();
    }
    
    @Override
    public int mapCacheSize() {
        return mapCacheManager.mapCacheSize();
    }
    
    @Override
    public List<String> getMapKeys() {
        return mapCacheManager.getMapKeys();
    }
    
    @Override
    public int getMapSize(String cacheKey) {
        return mapCacheManager.getMapSize(cacheKey);
    }
    
    // Unified methods
    @Override
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // List cache statistics
        stats.put("listCacheCount", size());
        stats.put("listCacheKeys", getKeys());
        
        // Map cache statistics
        stats.put("mapCacheCount", mapCacheSize());
        stats.put("mapCacheKeys", getMapKeys());
        
        // Combined statistics
        stats.put("totalCacheCount", getTotalCacheCount());
        
        // Individual cache sizes
        Map<String, Integer> listCacheSizes = new HashMap<>();
        for (String key : getKeys()) {
            get(key).ifPresent(data -> listCacheSizes.put(key, data.size()));
        }
        stats.put("listCacheSizes", listCacheSizes);
        
        Map<String, Integer> mapCacheSizes = new HashMap<>();
        for (String key : getMapKeys()) {
            mapCacheSizes.put(key, getMapSize(key));
        }
        stats.put("mapCacheSizes", mapCacheSizes);
        
        return stats;
    }
    
    @Override
    public Map<String, Integer> refreshAllCaches() {
        log.info("Refreshing all caches (list and map)");
        
        int listRefreshCount = refreshAll();
        int mapRefreshCount = refreshAllMaps();
        
        Map<String, Integer> results = new HashMap<>();
        results.put("listCacheRefreshCount", listRefreshCount);
        results.put("mapCacheRefreshCount", mapRefreshCount);
        results.put("totalRefreshCount", listRefreshCount + mapRefreshCount);
        
        log.info("Completed refreshing all caches. List: {}, Map: {}, Total: {}", 
                listRefreshCount, mapRefreshCount, listRefreshCount + mapRefreshCount);
        
        return results;
    }
    
    @Override
    public void clearAllCaches() {
        log.info("Clearing all caches (list and map)");
        
        int listCount = size();
        int mapCount = mapCacheSize();
        
        clear();
        clearMaps();
        
        log.info("Cleared all caches. List caches: {}, Map caches: {}", listCount, mapCount);
    }
    
    @Override
    public int getTotalCacheCount() {
        return size() + mapCacheSize();
    }
    
    @Override
    public boolean containsAnyKey(String key) {
        return containsKey(key) || containsMapKey(key);
    }
    
    @Override
    public boolean removeFromAllCaches(String key) {
        boolean removedFromList = remove(key);
        boolean removedFromMap = removeMap(key);
        return removedFromList || removedFromMap;
    }
}
