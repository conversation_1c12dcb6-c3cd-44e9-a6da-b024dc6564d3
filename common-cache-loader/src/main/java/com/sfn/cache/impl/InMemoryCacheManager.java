package com.sfn.cache.impl;

import com.sfn.cache.CacheManager;
import com.sfn.cache.RefreshStrategy;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Thread-safe in-memory implementation of CacheManager.
 * Uses ConcurrentHashMap for thread-safe operations and ReadWriteLock for refresh operations.
 *
 * @param <T> The type of objects stored in the cache
 */
@Log4j2
@Component
public class InMemoryCacheManager<T> implements CacheManager<T> {
    
    private final Map<String, List<T>> cache = new ConcurrentHashMap<>();
    private final Map<String, RefreshStrategy<T>> refreshStrategies = new ConcurrentHashMap<>();
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    
    @Override
    public void put(String key, List<T> data) {
        if (key == null) {
            throw new IllegalArgumentException("Cache key cannot be null");
        }
        if (data == null) {
            data = new ArrayList<>();
        }
        
        lock.writeLock().lock();
        try {
            cache.put(key, new ArrayList<>(data)); // Create defensive copy
            log.debug("Cached data for key: {} with {} items", key, data.size());
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    @Override
    public Optional<List<T>> get(String key) {
        if (key == null) {
            return Optional.empty();
        }
        
        lock.readLock().lock();
        try {
            List<T> data = cache.get(key);
            if (data != null) {
                log.debug("Cache hit for key: {} with {} items", key, data.size());
                return Optional.of(new ArrayList<>(data)); // Return defensive copy
            } else {
                log.debug("Cache miss for key: {}", key);
                return Optional.empty();
            }
        } finally {
            lock.readLock().unlock();
        }
    }
    
    @Override
    public void registerRefreshStrategy(String key, RefreshStrategy<T> refreshStrategy) {
        if (key == null) {
            throw new IllegalArgumentException("Cache key cannot be null");
        }
        if (refreshStrategy == null) {
            throw new IllegalArgumentException("Refresh strategy cannot be null");
        }
        
        refreshStrategies.put(key, refreshStrategy);
        log.debug("Registered refresh strategy for key: {}", key);
    }
    
    @Override
    public boolean refresh(String key) {
        if (key == null) {
            log.warn("Cannot refresh cache with null key");
            return false;
        }
        
        RefreshStrategy<T> strategy = refreshStrategies.get(key);
        if (strategy == null) {
            log.warn("No refresh strategy found for key: {}", key);
            return false;
        }
        
        try {
            log.info("Refreshing cache for key: {}", key);
            List<T> newData = strategy.loadData();
            put(key, newData);
            log.info("Successfully refreshed cache for key: {} with {} items", key, 
                    newData != null ? newData.size() : 0);
            return true;
        } catch (Exception e) {
            log.error("Failed to refresh cache for key: {}", key, e);
            return false;
        }
    }
    
    @Override
    public int refreshAll() {
        log.info("Refreshing all caches. Total strategies: {}", refreshStrategies.size());
        int successCount = 0;
        
        for (String key : refreshStrategies.keySet()) {
            if (refresh(key)) {
                successCount++;
            }
        }
        
        log.info("Completed refreshing all caches. Success: {}/{}", successCount, refreshStrategies.size());
        return successCount;
    }
    
    @Override
    public boolean containsKey(String key) {
        if (key == null) {
            return false;
        }
        
        lock.readLock().lock();
        try {
            return cache.containsKey(key);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    @Override
    public boolean remove(String key) {
        if (key == null) {
            return false;
        }
        
        lock.writeLock().lock();
        try {
            List<T> removed = cache.remove(key);
            refreshStrategies.remove(key);
            boolean wasRemoved = removed != null;
            if (wasRemoved) {
                log.debug("Removed cache entry for key: {}", key);
            }
            return wasRemoved;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    @Override
    public void clear() {
        lock.writeLock().lock();
        try {
            int size = cache.size();
            cache.clear();
            refreshStrategies.clear();
            log.info("Cleared all cache entries. Removed {} entries", size);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    @Override
    public int size() {
        lock.readLock().lock();
        try {
            return cache.size();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    @Override
    public List<String> getKeys() {
        lock.readLock().lock();
        try {
            return new ArrayList<>(cache.keySet());
        } finally {
            lock.readLock().unlock();
        }
    }
}
