package com.sfn.cache.impl;

import com.sfn.cache.MapCacheManager;
import com.sfn.cache.MapRefreshStrategy;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Thread-safe in-memory implementation of MapCacheManager.
 * Uses ConcurrentHashMap for thread-safe operations and ReadWriteLock for refresh operations.
 */
@Log4j2
@Component
public class InMemoryMapCacheManager implements MapCacheManager {

    private final Map<String, Map<String, String>> mapCache = new ConcurrentHashMap<>();
    private final Map<String, MapRefreshStrategy> mapRefreshStrategies = new ConcurrentHashMap<>();
    private final ReadWriteLock mapLock = new ReentrantReadWriteLock();
    
    @Override
    public void putMap(String key, Map<K, V> data) {
        if (key == null) {
            throw new IllegalArgumentException("Cache key cannot be null");
        }
        if (data == null) {
            data = new HashMap<>();
        }
        
        mapLock.writeLock().lock();
        try {
            mapCache.put(key, new HashMap<>(data)); // Create defensive copy
            log.debug("Cached map data for key: {} with {} entries", key, data.size());
        } finally {
            mapLock.writeLock().unlock();
        }
    }
    
    @Override
    public Optional<Map<K, V>> getMap(String key) {
        if (key == null) {
            return Optional.empty();
        }
        
        mapLock.readLock().lock();
        try {
            Map<K, V> data = mapCache.get(key);
            if (data != null) {
                log.debug("Map cache hit for key: {} with {} entries", key, data.size());
                return Optional.of(new HashMap<>(data)); // Return defensive copy
            } else {
                log.debug("Map cache miss for key: {}", key);
                return Optional.empty();
            }
        } finally {
            mapLock.readLock().unlock();
        }
    }
    
    @Override
    public Optional<V> getMapValue(String cacheKey, K mapKey) {
        if (cacheKey == null || mapKey == null) {
            return Optional.empty();
        }
        
        mapLock.readLock().lock();
        try {
            Map<K, V> cachedMap = mapCache.get(cacheKey);
            if (cachedMap != null) {
                V value = cachedMap.get(mapKey);
                if (value != null) {
                    log.debug("Map value cache hit for cache key: {}, map key: {}", cacheKey, mapKey);
                    return Optional.of(value);
                }
            }
            log.debug("Map value cache miss for cache key: {}, map key: {}", cacheKey, mapKey);
            return Optional.empty();
        } finally {
            mapLock.readLock().unlock();
        }
    }
    
    @Override
    public boolean putMapValue(String cacheKey, K mapKey, V value) {
        if (cacheKey == null || mapKey == null) {
            return false;
        }
        
        mapLock.writeLock().lock();
        try {
            Map<K, V> cachedMap = mapCache.get(cacheKey);
            if (cachedMap != null) {
                cachedMap.put(mapKey, value);
                log.debug("Updated map value for cache key: {}, map key: {}", cacheKey, mapKey);
                return true;
            }
            log.warn("Cannot put map value - cache key not found: {}", cacheKey);
            return false;
        } finally {
            mapLock.writeLock().unlock();
        }
    }
    
    @Override
    public boolean removeMapValue(String cacheKey, K mapKey) {
        if (cacheKey == null || mapKey == null) {
            return false;
        }
        
        mapLock.writeLock().lock();
        try {
            Map<K, V> cachedMap = mapCache.get(cacheKey);
            if (cachedMap != null) {
                V removed = cachedMap.remove(mapKey);
                boolean wasRemoved = removed != null;
                if (wasRemoved) {
                    log.debug("Removed map value for cache key: {}, map key: {}", cacheKey, mapKey);
                }
                return wasRemoved;
            }
            return false;
        } finally {
            mapLock.writeLock().unlock();
        }
    }
    
    @Override
    public void registerMapRefreshStrategy(String key, MapRefreshStrategy<K, V> refreshStrategy) {
        if (key == null) {
            throw new IllegalArgumentException("Cache key cannot be null");
        }
        if (refreshStrategy == null) {
            throw new IllegalArgumentException("Refresh strategy cannot be null");
        }
        
        mapRefreshStrategies.put(key, refreshStrategy);
        log.debug("Registered map refresh strategy for key: {}", key);
    }
    
    @Override
    public boolean refreshMap(String key) {
        if (key == null) {
            log.warn("Cannot refresh map cache with null key");
            return false;
        }
        
        MapRefreshStrategy<K, V> strategy = mapRefreshStrategies.get(key);
        if (strategy == null) {
            log.warn("No map refresh strategy found for key: {}", key);
            return false;
        }
        
        try {
            log.info("Refreshing map cache for key: {}", key);
            Map<K, V> newData = strategy.loadMapData();
            putMap(key, newData);
            log.info("Successfully refreshed map cache for key: {} with {} entries", key, 
                    newData != null ? newData.size() : 0);
            return true;
        } catch (Exception e) {
            log.error("Failed to refresh map cache for key: {}", key, e);
            return false;
        }
    }
    
    @Override
    public int refreshAllMaps() {
        log.info("Refreshing all map caches. Total strategies: {}", mapRefreshStrategies.size());
        int successCount = 0;
        
        for (String key : mapRefreshStrategies.keySet()) {
            if (refreshMap(key)) {
                successCount++;
            }
        }
        
        log.info("Completed refreshing all map caches. Success: {}/{}", successCount, mapRefreshStrategies.size());
        return successCount;
    }
    
    @Override
    public boolean containsMapKey(String key) {
        if (key == null) {
            return false;
        }
        
        mapLock.readLock().lock();
        try {
            return mapCache.containsKey(key);
        } finally {
            mapLock.readLock().unlock();
        }
    }
    
    @Override
    public boolean containsMapValue(String cacheKey, K mapKey) {
        if (cacheKey == null || mapKey == null) {
            return false;
        }
        
        mapLock.readLock().lock();
        try {
            Map<K, V> cachedMap = mapCache.get(cacheKey);
            return cachedMap != null && cachedMap.containsKey(mapKey);
        } finally {
            mapLock.readLock().unlock();
        }
    }
    
    @Override
    public boolean removeMap(String key) {
        if (key == null) {
            return false;
        }
        
        mapLock.writeLock().lock();
        try {
            Map<K, V> removed = mapCache.remove(key);
            mapRefreshStrategies.remove(key);
            boolean wasRemoved = removed != null;
            if (wasRemoved) {
                log.debug("Removed map cache entry for key: {}", key);
            }
            return wasRemoved;
        } finally {
            mapLock.writeLock().unlock();
        }
    }
    
    @Override
    public void clearMaps() {
        mapLock.writeLock().lock();
        try {
            int size = mapCache.size();
            mapCache.clear();
            mapRefreshStrategies.clear();
            log.info("Cleared all map cache entries. Removed {} entries", size);
        } finally {
            mapLock.writeLock().unlock();
        }
    }
    
    @Override
    public int mapCacheSize() {
        mapLock.readLock().lock();
        try {
            return mapCache.size();
        } finally {
            mapLock.readLock().unlock();
        }
    }
    
    @Override
    public List<String> getMapKeys() {
        mapLock.readLock().lock();
        try {
            return new ArrayList<>(mapCache.keySet());
        } finally {
            mapLock.readLock().unlock();
        }
    }
    
    @Override
    public int getMapSize(String cacheKey) {
        if (cacheKey == null) {
            return 0;
        }
        
        mapLock.readLock().lock();
        try {
            Map<K, V> cachedMap = mapCache.get(cacheKey);
            return cachedMap != null ? cachedMap.size() : 0;
        } finally {
            mapLock.readLock().unlock();
        }
    }
}
