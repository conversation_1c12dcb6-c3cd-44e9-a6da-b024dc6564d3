package com.sfn.cache;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Unified cache manager interface that supports both List<T> and Map<K, V> caching.
 * Combines the functionality of CacheManager and MapCacheManager.
 *
 * @param <T> The type of objects stored in list caches
 * @param <K> The type of keys in the map
 * @param <V> The type of values in the map
 */
public interface UnifiedCacheManager<T, K, V> extends CacheManager<T>, MapCacheManager<K, V> {
    
    /**
     * Get comprehensive cache statistics
     *
     * @return Map containing cache statistics
     */
    Map<String, Object> getCacheStatistics();
    
    /**
     * Refresh all caches (both list and map)
     *
     * @return Map with refresh results
     */
    Map<String, Integer> refreshAllCaches();
    
    /**
     * Clear all caches (both list and map)
     */
    void clearAllCaches();
    
    /**
     * Get total cache count (list + map caches)
     *
     * @return Total number of cached entries
     */
    int getTotalCacheCount();
    
    /**
     * Check if any cache key exists (in either list or map caches)
     *
     * @param key The cache key to check
     * @return true if key exists in any cache type
     */
    boolean containsAnyKey(String key);
    
    /**
     * Remove a key from both list and map caches
     *
     * @param key The cache key to remove
     * @return true if key was removed from any cache
     */
    boolean removeFromAllCaches(String key);
}
