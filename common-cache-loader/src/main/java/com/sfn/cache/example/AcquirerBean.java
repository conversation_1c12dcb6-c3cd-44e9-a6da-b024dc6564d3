package com.sfn.cache.example;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Example bean representing an Acquirer entity.
 * This demonstrates how to use the cache library with domain objects.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AcquirerBean {
    
    private String id;
    private String name;
    private String url;
    private boolean active;
    private String description;
    
    public AcquirerBean(String name, String url) {
        this.name = name;
        this.url = url;
        this.active = true;
    }
}
