package com.sfn.cache.example;

import com.sfn.cache.CacheManager;
import com.sfn.cache.annotation.CacheInitializer;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;

/**
 * Example service demonstrating how to use the cache library.
 * Shows proper initialization, registration of refresh strategies, and usage patterns.
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class ExampleCacheService {
    
    private final CacheManager<AcquirerBean> cacheManager;
    private final AcquirerRefreshStrategy acquirerRefreshStrategy;
    
    private static final String ACQUIRER_CACHE_KEY = "acquirer";
    
    /**
     * Initialize cache with refresh strategies and initial data.
     * This method is called automatically after Spring context initialization.
     */
    @PostConstruct
    @CacheInitializer(order = 1)
    public void initializeCache() {
        log.info("Initializing cache with acquirer data");
        
        try {
            // Register refresh strategy
            cacheManager.registerRefreshStrategy(ACQUIRER_CACHE_KEY, acquirerRefreshStrategy);
            log.info("Registered refresh strategy for key: {}", ACQUIRER_CACHE_KEY);
            
            // Load initial data
            boolean refreshSuccess = cacheManager.refresh(ACQUIRER_CACHE_KEY);
            if (refreshSuccess) {
                log.info("Successfully initialized cache for key: {}", ACQUIRER_CACHE_KEY);
            } else {
                log.error("Failed to initialize cache for key: {}", ACQUIRER_CACHE_KEY);
            }
            
        } catch (Exception e) {
            log.error("Error during cache initialization", e);
            // Depending on configuration, you might want to throw the exception
            // to fail application startup if cache initialization is critical
        }
    }
    
    /**
     * Get all acquirers from cache
     *
     * @return List of acquirer beans, or empty list if not cached
     */
    public List<AcquirerBean> getAllAcquirers() {
        Optional<List<AcquirerBean>> cachedData = cacheManager.get(ACQUIRER_CACHE_KEY);
        
        if (cachedData.isPresent()) {
            log.debug("Retrieved {} acquirers from cache", cachedData.get().size());
            return cachedData.get();
        } else {
            log.warn("No acquirer data found in cache");
            return List.of(); // Return empty list instead of null
        }
    }
    
    /**
     * Get active acquirers only
     *
     * @return List of active acquirer beans
     */
    public List<AcquirerBean> getActiveAcquirers() {
        return getAllAcquirers().stream()
                .filter(AcquirerBean::isActive)
                .toList();
    }
    
    /**
     * Find acquirer by name
     *
     * @param name The acquirer name to search for
     * @return Optional containing the acquirer if found
     */
    public Optional<AcquirerBean> findAcquirerByName(String name) {
        return getAllAcquirers().stream()
                .filter(acquirer -> acquirer.getName().equalsIgnoreCase(name))
                .findFirst();
    }
    
    /**
     * Manually refresh acquirer cache
     *
     * @return true if refresh was successful
     */
    public boolean refreshAcquirerCache() {
        log.info("Manually refreshing acquirer cache");
        return cacheManager.refresh(ACQUIRER_CACHE_KEY);
    }
    
    /**
     * Check if acquirer cache is populated
     *
     * @return true if cache contains acquirer data
     */
    public boolean isAcquirerCachePopulated() {
        return cacheManager.containsKey(ACQUIRER_CACHE_KEY) && 
               !getAllAcquirers().isEmpty();
    }
    
    /**
     * Get cache statistics for acquirer data
     *
     * @return String with cache statistics
     */
    public String getCacheStats() {
        if (cacheManager.containsKey(ACQUIRER_CACHE_KEY)) {
            int totalCount = getAllAcquirers().size();
            int activeCount = getActiveAcquirers().size();
            return String.format("Acquirer Cache - Total: %d, Active: %d", totalCount, activeCount);
        } else {
            return "Acquirer Cache - Not initialized";
        }
    }
}
