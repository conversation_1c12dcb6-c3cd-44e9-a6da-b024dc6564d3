package com.sfn.cache.example;

import com.sfn.cache.RefreshStrategy;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Example implementation of RefreshStrategy for Acquirer data.
 * This demonstrates how to implement the strategy pattern for cache refresh.
 */
@Log4j2
@Component
public class AcquirerRefreshStrategy implements RefreshStrategy<AcquirerBean> {
    
    @Override
    public List<AcquirerBean> loadData() throws Exception {
        log.info("Loading acquirer data from data source");
        
        // Simulate data loading from database or external service
        // In real implementation, this would call repository or service
        try {
            // Simulate some processing time
            Thread.sleep(100);
            
            List<AcquirerBean> acquirers = Arrays.asList(
                new AcquirerBean("1", "Bank of America", "https://boa.com", true, "Major US Bank"),
                new AcquirerBean("2", "Chase Bank", "https://chase.com", true, "JPMorgan Chase Bank"),
                new AcquirerBean("3", "Wells Fargo", "https://wellsfargo.com", true, "Wells Fargo Bank"),
                new AcquirerBean("4", "Citibank", "https://citibank.com", false, "Citibank NA")
            );
            
            log.info("Successfully loaded {} acquirer records", acquirers.size());
            return acquirers;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new Exception("Data loading was interrupted", e);
        } catch (Exception e) {
            log.error("Failed to load acquirer data", e);
            throw new Exception("Failed to load acquirer data from source", e);
        }
    }
}
