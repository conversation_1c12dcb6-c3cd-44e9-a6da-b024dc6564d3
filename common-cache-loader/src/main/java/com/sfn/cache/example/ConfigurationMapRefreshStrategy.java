package com.sfn.cache.example;

import com.sfn.cache.MapRefreshStrategy;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Example implementation of MapRefreshStrategy for configuration data.
 * This demonstrates how to implement the strategy pattern for map cache refresh.
 */
@Log4j2
@Component
public class ConfigurationMapRefreshStrategy implements MapRefreshStrategy<String, String> {
    
    @Override
    public Map<String, String> loadMapData() throws Exception {
        log.info("Loading configuration data from data source");
        
        try {
            // Simulate data loading from database, properties file, or external service
            // In real implementation, this would call repository or configuration service
            Thread.sleep(50); // Simulate processing time
            
            Map<String, String> configurations = new HashMap<>();
            configurations.put("app.name", "Payment Gateway");
            configurations.put("app.version", "1.4.0");
            configurations.put("app.environment", "production");
            configurations.put("database.timeout", "30000");
            configurations.put("api.rate.limit", "1000");
            configurations.put("cache.ttl", "3600");
            configurations.put("security.jwt.expiry", "86400");
            configurations.put("notification.email.enabled", "true");
            configurations.put("notification.sms.enabled", "false");
            configurations.put("feature.new.payment.method", "true");
            configurations.put("maintenance.mode", "false");
            configurations.put("logging.level", "INFO");
            
            log.info("Successfully loaded {} configuration entries", configurations.size());
            return configurations;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new Exception("Configuration data loading was interrupted", e);
        } catch (Exception e) {
            log.error("Failed to load configuration data", e);
            throw new Exception("Failed to load configuration data from source", e);
        }
    }
}
