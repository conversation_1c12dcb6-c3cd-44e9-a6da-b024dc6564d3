package com.sfn.cache.example;

import com.sfn.cache.UnifiedCacheManager;
import com.sfn.cache.annotation.CacheInitializer;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Example service demonstrating how to use the unified cache library with both list and map caching.
 * Shows proper initialization, registration of refresh strategies, and usage patterns for both cache types.
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class UnifiedCacheExampleService {
    
    private final UnifiedCacheManager<AcquirerBean> cacheManager;
    private final AcquirerRefreshStrategy acquirerRefreshStrategy;
    private final ConfigurationMapRefreshStrategy configurationMapRefreshStrategy;
    
    private static final String ACQUIRER_CACHE_KEY = "acquirer";
    private static final String CONFIG_CACHE_KEY = "configuration";
    
    /**
     * Initialize both list and map caches with refresh strategies and initial data.
     * This method is called automatically after Spring context initialization.
     */
    @PostConstruct
    @CacheInitializer(order = 1)
    public void initializeCaches() {
        log.info("Initializing unified cache with acquirer and configuration data");
        
        try {
            // Register list cache refresh strategy
            unifiedCacheManager.registerRefreshStrategy(ACQUIRER_CACHE_KEY, acquirerRefreshStrategy);
            log.info("Registered list refresh strategy for key: {}", ACQUIRER_CACHE_KEY);
            
            // Register map cache refresh strategy
            unifiedCacheManager.registerMapRefreshStrategy(CONFIG_CACHE_KEY, configurationMapRefreshStrategy);
            log.info("Registered map refresh strategy for key: {}", CONFIG_CACHE_KEY);
            
            // Load initial data for both caches
            Map<String, Integer> refreshResults = unifiedCacheManager.refreshAllCaches();
            log.info("Cache initialization completed. Results: {}", refreshResults);
            
        } catch (Exception e) {
            log.error("Error during unified cache initialization", e);
        }
    }
    
    // List Cache Operations
    
    /**
     * Get all acquirers from list cache
     *
     * @return List of acquirer beans, or empty list if not cached
     */
    public List<AcquirerBean> getAllAcquirers() {
        Optional<List<AcquirerBean>> cachedData = unifiedCacheManager.get(ACQUIRER_CACHE_KEY);
        
        if (cachedData.isPresent()) {
            log.debug("Retrieved {} acquirers from list cache", cachedData.get().size());
            return cachedData.get();
        } else {
            log.warn("No acquirer data found in list cache");
            return List.of();
        }
    }
    
    /**
     * Get active acquirers only
     *
     * @return List of active acquirer beans
     */
    public List<AcquirerBean> getActiveAcquirers() {
        return getAllAcquirers().stream()
                .filter(AcquirerBean::isActive)
                .toList();
    }
    
    // Map Cache Operations
    
    /**
     * Get all configuration from map cache
     *
     * @return Map of configuration key-value pairs, or empty map if not cached
     */
    public Map<String, String> getAllConfigurations() {
        Optional<Map<String, String>> cachedData = unifiedCacheManager.getMap(CONFIG_CACHE_KEY);
        
        if (cachedData.isPresent()) {
            log.debug("Retrieved {} configuration entries from map cache", cachedData.get().size());
            return cachedData.get();
        } else {
            log.warn("No configuration data found in map cache");
            return Map.of();
        }
    }
    
    /**
     * Get a specific configuration value
     *
     * @param configKey The configuration key
     * @return Optional containing the configuration value if found
     */
    public Optional<String> getConfigurationValue(String configKey) {
        return unifiedCacheManager.getMapValue(CONFIG_CACHE_KEY, configKey);
    }
    
    /**
     * Get configuration value with default
     *
     * @param configKey    The configuration key
     * @param defaultValue Default value if not found
     * @return Configuration value or default
     */
    public String getConfigurationValue(String configKey, String defaultValue) {
        return getConfigurationValue(configKey).orElse(defaultValue);
    }
    
    /**
     * Update a configuration value in cache
     *
     * @param configKey The configuration key
     * @param value     The new value
     * @return true if update was successful
     */
    public boolean updateConfigurationValue(String configKey, String value) {
        boolean updated = unifiedCacheManager.putMapValue(CONFIG_CACHE_KEY, configKey, value);
        if (updated) {
            log.info("Updated configuration: {} = {}", configKey, value);
        } else {
            log.warn("Failed to update configuration: {}", configKey);
        }
        return updated;
    }
    
    /**
     * Check if a configuration key exists
     *
     * @param configKey The configuration key
     * @return true if the key exists
     */
    public boolean hasConfiguration(String configKey) {
        return unifiedCacheManager.containsMapValue(CONFIG_CACHE_KEY, configKey);
    }
    
    // Unified Operations
    
    /**
     * Refresh all caches (both list and map)
     *
     * @return Map with refresh results
     */
    public Map<String, Integer> refreshAllCaches() {
        log.info("Manually refreshing all caches");
        return unifiedCacheManager.refreshAllCaches();
    }
    
    /**
     * Refresh specific cache type
     *
     * @param cacheType "list" or "map"
     * @param key       The cache key
     * @return true if refresh was successful
     */
    public boolean refreshSpecificCache(String cacheType, String key) {
        log.info("Manually refreshing {} cache for key: {}", cacheType, key);
        
        if ("list".equalsIgnoreCase(cacheType)) {
            return unifiedCacheManager.refresh(key);
        } else if ("map".equalsIgnoreCase(cacheType)) {
            return unifiedCacheManager.refreshMap(key);
        } else {
            log.warn("Unknown cache type: {}", cacheType);
            return false;
        }
    }
    
    /**
     * Get comprehensive cache statistics
     *
     * @return Map containing cache statistics
     */
    public Map<String, Object> getCacheStatistics() {
        return unifiedCacheManager.getCacheStatistics();
    }
    
    /**
     * Check if any cache is populated
     *
     * @return true if any cache contains data
     */
    public boolean isAnyCachePopulated() {
        return unifiedCacheManager.getTotalCacheCount() > 0 && 
               (!getAllAcquirers().isEmpty() || !getAllConfigurations().isEmpty());
    }
    
    /**
     * Get cache health summary
     *
     * @return String with cache health information
     */
    public String getCacheHealthSummary() {
        Map<String, Object> stats = getCacheStatistics();
        
        int listCacheCount = (Integer) stats.get("listCacheCount");
        int mapCacheCount = (Integer) stats.get("mapCacheCount");
        int totalCount = (Integer) stats.get("totalCacheCount");
        
        int acquirerCount = getAllAcquirers().size();
        int configCount = getAllConfigurations().size();
        
        return String.format(
            "Cache Health - Total Caches: %d (List: %d, Map: %d), " +
            "Acquirers: %d, Configurations: %d",
            totalCount, listCacheCount, mapCacheCount, acquirerCount, configCount
        );
    }
    
    // Utility Methods
    
    /**
     * Check if maintenance mode is enabled
     *
     * @return true if maintenance mode is enabled
     */
    public boolean isMaintenanceModeEnabled() {
        return "true".equalsIgnoreCase(getConfigurationValue("maintenance.mode", "false"));
    }
    
    /**
     * Get application version from configuration
     *
     * @return Application version or "unknown"
     */
    public String getApplicationVersion() {
        return getConfigurationValue("app.version", "unknown");
    }
    
    /**
     * Check if a feature is enabled
     *
     * @param featureName The feature name (will be prefixed with "feature.")
     * @return true if feature is enabled
     */
    public boolean isFeatureEnabled(String featureName) {
        String key = "feature." + featureName;
        return "true".equalsIgnoreCase(getConfigurationValue(key, "false"));
    }
}
