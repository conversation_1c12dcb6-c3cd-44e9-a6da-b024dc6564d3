package com.sfn.cache;

import java.util.List;

/**
 * Strategy interface for refreshing cache data.
 * Implementations define how to load/reload data for specific cache entries.
 *
 * @param <T> The type of objects to be loaded
 */
@FunctionalInterface
public interface RefreshStrategy<T> {
    
    /**
     * Load data for the cache.
     * This method should implement the logic to fetch fresh data from the data source.
     *
     * @return List of data to be cached
     * @throws Exception if data loading fails
     */
    List<T> loadData() throws Exception;
}
