<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.0</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.sfn</groupId>
    <artifactId>common-modules</artifactId>
    <version>1.4.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>activemq-connector</module>
        <module>api-retrier</module>
        <module>centralized-tracer</module>
        <module>send-otp-mfa</module>
        <module>common-logging</module>
        <module>common-dependencies</module>
        <module>request-response-logger</module>
        <module>common-cache-loader</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <distributionManagement>
        <repository>
            <id>nexus</id>
            <url>https://nexus.mosambee.in:8443/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus</id>
            <url>https://nexus.mosambee.in:8443/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>